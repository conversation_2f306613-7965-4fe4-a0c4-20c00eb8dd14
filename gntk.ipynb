#!/usr/bin/env python
# coding: utf-8

# In[4]:


#!/usr/bin/env python3
import csv
import os
import urllib.request
from urllib.parse import urlparse

# Pf3k study: ERP000190
URL = (
    "https://www.ebi.ac.uk/ena/portal/api/filereport"
    "?accession=ERP000190&result=read_run"
    "&fields=run_accession,scientific_name,library_strategy,library_layout,"
    "fastq_ftp,fastq_md5,fastq_bytes,country"
    "&format=tsv")

OUTDIR = "pf3k_one_sample"
os.makedirs(OUTDIR, exist_ok=True)

def to_https(u: str) -> str:
    """Normalize any ENA 'fastq_ftp' value to a valid HTTPS URL."""
    u = (u or "").strip()
    if not u:
        return u
    # Cases we might see:
    # 1) ftp://ftp.sra.ebi.ac.uk/...  ->  https://ftp.sra.ebi.ac.uk/...
    if u.startswith("ftp://ftp."):
        return "https://" + u[len("ftp://"):]
    # 2) ftp://sra.ebi.ac.uk/... (rare)
    if u.startswith("ftp://"):
        return "https://" + u[len("ftp://"):]
    # 3) //ftp.sra.ebi.ac.uk/...
    if u.startswith("//"):
        return "https:" + u
    # 4) http://ftp.sra.ebi.ac.uk/...
    if u.startswith("http://"):
        return "https://" + u[len("http://"):]
    # 5) already https://...
    if u.startswith("https://"):
        return u
    # 6) no scheme: ftp.sra.ebi.ac.uk/vol1/fastq/...
    if u.startswith("ftp.sra.ebi.ac.uk/"):
        return "https://" + u
    # 7) fallback: if there is still no scheme, prepend https://
    parsed = urlparse(u)
    if not parsed.scheme:
        return "https://" + u
    return u

print("[INFO] Fetching metadata…")
resp = urllib.request.urlopen(URL)
lines = resp.read().decode("utf-8").splitlines()
reader = csv.DictReader(lines, delimiter="\t")

# Pick the first sample: P. falciparum + WGS + PAIRED and fastq links present
sample = None
for row in reader:
    if (
        "falciparum" in (row.get("scientific_name") or "").lower()
        and (row.get("library_strategy") or "").upper() == "WGS"
        and (row.get("library_layout") or "").upper() == "PAIRED"
        and (row.get("fastq_ftp") or "").strip()
    ):
        sample = row
        break

if not sample:
    raise SystemExit("No matching sample found!")

print("[INFO] Selected RUN:", sample.get("run_accession"), sample.get("country", "N/A"))

# Usually two files (R1 and R2); ';' is the separator in 'fastq_ftp'
raw_links = [s.strip() for s in (sample.get("fastq_ftp") or "").split(";") if s.strip()]
if not raw_links:
    raise SystemExit("No FASTQ links in fastq_ftp")

# Download
for raw in raw_links:
    https_url = to_https(raw)

    # Safety check: enforce https:// scheme
    if not https_url.startswith("https://"):
        raise ValueError(f"URL normalization failed: {raw} -> {https_url}")

    filename = os.path.join(OUTDIR, os.path.basename(raw))
    print("[INFO] Downloading", https_url, "->", filename)
    req = urllib.request.Request(https_url, headers={"User-Agent": "pf3k-downloader/1.0"})
    with urllib.request.urlopen(req) as r, open(filename, "wb") as f:
        f.write(r.read())

print("[DONE] All files downloaded into", OUTDIR)
